<template>
  <view class="shipment-detail-page">
    <!-- 头部信息 -->
    <view class="header-info">
      <view class="info-content">
        <view class="info-item">
          <view class="label1">发货单号</view>
          <view class="value1">{{ shipmentDetail.deliveryOrderNo }}</view>
        </view>
        <view class="info-item">
          <text class="label">供应商名称：</text>
          <text class="value">{{ shipmentDetail.supplierName }}</text>
        </view>
        <view class="info-item">
          <text class="label">企业前缀：</text>
          <text class="value">{{ shipmentDetail.supplierPrefix }}</text>
        </view>
      </view>
      <view>
        <image
          style="width: 100rpx; height: 120rpx"
          src="../../../images/plan.png"
          mode="aspectFit"
        >
        </image>
      </view>
    </view>

    <!-- 明细信息 -->
    <view class="detail-section">
      <view class="section-title">
        <text>明细信息</text>
      </view>

      <view class="detail-list">
        <view
          class="detail-item"
          v-for="(item, index) in shipmentDetail.details"
          :key="index"
        >
          <view class="item-content">
            <!-- 状态标签和计划编号 -->
            <view class="item-header">
              <view
                class="item-status urgent"
                v-if="!item.actualQuantity && status === '2'"
                >未填报</view
              >
              <text class="plan-number">计划编号：{{ item.planNo }}</text>
            </view>

            <!-- 详细信息网格布局 -->
            <view class="item-details">
              <view class="detail-row">
                <view class="detail-col">
                  <text class="label">关联采购单号：</text>
                  <text class="value">{{ item.purchaseOrderNo }}</text>
                </view>
              </view>
              <view class="detail-row">
                <view class="detail-col">
                  <text class="label">物料名称：</text>
                  <text class="value">{{ item.materialName }}</text>
                </view>
              </view>

              <view class="detail-row">
                <view class="detail-col">
                  <text class="label">计划交货时间：</text>
                  <text class="value">{{ item.planDeliveryTime }}</text>
                </view>
                <view class="detail-col">
                  <text class="label">明细行数：</text>
                  <text class="value">{{
                    item?.deliveryOrderHerbInfoVOList.length ?? "0"
                  }}</text>
                </view>
              </view>

              <view class="detail-row">
                <view class="detail-col">
                  <text class="label">计划数量：</text>
                  <text class="value"
                    >{{ item.planQuantity }}{{ item.unitName }}</text
                  >
                </view>
                <view class="detail-col">
                  <text class="label">实发数量：</text>
                  <text class="value"
                    >{{ calculateActualQuantity(item)
                    }}{{ item.unitName }}</text
                  >
                </view>
              </view>
            </view>
          </view>
          <view @click="toDetailForm(item, '2')" v-if="status === '2'">
            <image
              style="width: 32rpx; height: 32rpx"
              src="../../../images/edit.png"
              mode="aspectFit"
            >
            </image>
          </view>
          <view @click="toDetailForm(item, '3')" v-if="status === '3'">
            <image
              style="width: 32rpx; height: 32rpx"
              src="../../../images/info.png"
              mode="aspectFit"
            >
            </image>
          </view>
        </view>
      </view>
    </view>

    <!-- 发货信息折叠面板 -->
    <view class="shipping-section">
      <view class="section-title">
        <text>发货信息</text>
        <view
          class="collapse-arrow"
          :class="{ expanded: isShippingExpanded }"
          @click="toggleShipping"
        >
          <IconFont name="rect-down" size="16" color="#1A43AD" />
        </view>
      </view>
      <view class="collapse-content" v-show="isShippingExpanded">
        <view class="shipping-info">
          <view class="info-item">
            <text class="info-label">发货时间</text>
            <text class="info-value">{{ shipmentDetail.deliveryTime }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">运输类型</text>
            <text class="info-value">{{
              shipmentDetail.transportType === "1" ? "自有车辆" : "第三方车辆"
            }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">司机电话</text>
            <text class="info-value">{{ shipmentDetail.driverPhone }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">车牌号</text>
            <text class="info-value">{{ shipmentDetail.licensePlate }}</text>
          </view>
          <view class="info-item" v-if="shipmentDetail.deliveryRemarkEnabled">
            <text class="info-label">发货方备注</text>
            <text class="info-value">对方备注给你</text>
          </view>
          <view class="info-item" v-if="shipmentDetail.deliveryRemarkEnabled">
            <text class="info-textarea"
              ><text>{{ shipmentDetail.deliveryRemark }}</text></text
            >
          </view>
        </view>

        <!-- 图片区域 -->
        <view class="image-label">图片</view>
        <view class="image-gallery">
          <view
            class="image-item"
            v-for="(image, index) in shipmentDetail.deliveryImages"
            :key="index"
            @click="previewImage(image, index)"
          >
            <image :src="image" mode="aspectFill" class="gallery-image" />
          </view>
        </view>
      </view>
    </view>

    <!-- 收货作业表单 -->
    <view class="shipping-section" v-if="status === '2'">
      <view class="section-title">
        <text>收货作业</text>
      </view>
      <view class="shipping-info">
        <!-- 收货备注开关 -->
        <view class="info-item switch-item">
          <text class="info-label">收货备注</text>
          <view class="switch-container">
            <text>备注给对方</text>
            <nut-switch
              active-color="#1A43AD"
              size="small"
              v-model="receivingForm.remarksEnabled"
            />
          </view>
        </view>
      </view>
      <!-- 备注输入框 -->
      <view class="image-label">备注内容</view>
      <nut-textarea
        v-model="receivingForm.remarks"
        placeholder="请输入备注"
        :rows="4"
        :max-length="200"
        class="info-textarea"
      />
      <!-- 图片上传 -->
      <view class="image-label">图片</view>
      <view class="image-gallery">
        <ImageUploader
          v-model="receivingForm.receiveImages"
          :maximum="3"
          :max-size="4"
          @upload-success="onUploadSuccess"
          @upload-error="onUploadError"
          @delete="onImageDelete"
        />
      </view>
    </view>

    <!-- 收货信息折叠面板 -->
    <view class="detail-section" v-if="status === '3'">
      <view class="section-title">
        <text>收货信息</text>
        <view
          class="collapse-arrow"
          :class="{ expanded: isReceiving }"
          @click="toggleReceiving"
        >
          <IconFont name="rect-down" size="16" color="#666" />
        </view>
      </view>
      <view class="collapse-content" v-show="isReceiving">
        <view class="shipping-info">
          <view class="info-item">
            <text class="info-label">收货时间</text>
            <text class="info-value">{{ shipmentDetail.receiptTime }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">收货备注</text>
          </view>
          <view class="info-item">
            <text class="info-textarea">{{
              shipmentDetail.receiptRemark ?? ""
            }}</text>
          </view>
        </view>

        <!-- 图片区域 -->
        <view class="image-label">图片</view>
        <view class="image-gallery">
          <view
            class="image-item"
            v-for="(image, index) in shipmentDetail.receiveImages"
            :key="index"
            @click="previewImage(image, index)"
          >
            <image :src="image" mode="aspectFill" class="gallery-image" />
          </view>
        </view>
      </view>
    </view>

    <!-- 确认收货按钮 -->
    <view class="confirm-section" v-if="status === '2'">
      <nut-button
        type="success"
        size="large"
        block
        :loading="confirmLoading"
        @click="confirmReceiving"
      >
        确认收货
      </nut-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { IconFont } from "@nutui/icons-vue-taro";
import { ShipmentDetail } from "@/types/shipment";
import { queryDeliveryOrderDetail } from "@/api/shipment";
import { confirmReceipt } from "@/api/receiving";
import { useReceivingStore } from "@/store/modules/receiving";
import { useReceivingFormStore } from "@/store/modules/receivingForm";

// 路由参数
const router = useRouter();
// 页面加载状态
const loading = ref(false);
// 获取发货状态
const status = ref<string | null>(null);
// 状态管理
const isReceiving = ref(true);
const images = ref<string[]>([]);
const receivingFormStore = useReceivingFormStore();
const receivingStore = useReceivingStore();
// 页面数据

useDidShow(() => {
  console.log(receivingStore.isEdit, "-------------");
  console.log("从 detailForm 编辑返回，执行刷新");
  if (receivingStore.isEdit) {
    // 从缓存receivingForm获取到id对应的actualQuantity,并更新列表
    const updatedDetails = [...shipmentDetail.value.details];

    // 遍历列表并尝试从缓存中更新 actualQuantity
    for (let i = 0; i < updatedDetails.length; i++) {
      const detail = updatedDetails[i];
      const cachedItem = receivingFormStore.getFormItem(detail.planNo);

      if (cachedItem?.receiptQuantity != null) {
        updatedDetails[i] = {
          ...detail,
          actualQuantity: cachedItem.receiptQuantity ?? null,
        };
      }
    }
    console.log(updatedDetails, "===================updatedDetails");
    // 更新主数据源
    shipmentDetail.value = {
      ...shipmentDetail.value,
      details: updatedDetails,
    };

    // 清除状态
    receivingStore.setIsEdit(false);
  }
});
// 计算实发数量总和
const calculateActualQuantity = (item: any) => {
  if (
    !item.deliveryOrderHerbInfoVOList ||
    !Array.isArray(item.deliveryOrderHerbInfoVOList)
  ) {
    return 0;
  }

  return item.deliveryOrderHerbInfoVOList.reduce(
    (total: number, herbInfo: any) => {
      const actualQuantity = parseFloat(herbInfo.actualQuantity) || 0;
      return total + actualQuantity;
    },
    0
  );
};
// 图片上传相关事件处理
const onUploadSuccess = (data: any) => {
  console.log("图片上传成功", data);
  images.value.push(data.data.url);
};

const onUploadError = (error: any) => {
  console.log("图片上传失败", error);
};

const onImageDelete = (file: any) => {
  console.log("图片删除", file);
  images.value.splice(file.index, 1);
};
onMounted(() => {
  // 获取页面路由参数
  const { deliveryOrderNo } = router.params;
  console.log(router.params, "------------------------");
  status.value = router.params.orderStatus ?? null;
  console.log(status.value, "status");
  if (deliveryOrderNo) {
    fetchShipmentDetail(deliveryOrderNo);
  }
});

// 发货单详情数据
const shipmentDetail = ref<ShipmentDetail>({
  deliveryOrderNo: "",
  customerName: "",
  deliveryAddress: "",
  transportType: "",
  driverPhone: "",
  status: "",
  licensePlate: "",
  details: [],
});

const receivingForm = reactive({
  remarksEnabled: true,
  remarks: "",
  receivingForm: "",
  receiveImages: [],
});

// 状态管理
const isShippingExpanded = ref(true); // 发货信息默认展开
const confirmLoading = ref(false);

// 切换发货信息展开状态
const toggleShipping = () => {
  isShippingExpanded.value = !isShippingExpanded.value;
};

const toggleReceiving = () => {
  isReceiving.value = !isReceiving.value;
};

// 图片预览
const previewImage = (imageUrl: string, _index: number) => {
  Taro.previewImage({
    current: imageUrl,
    urls: shipmentDetail.value.deliveryImages ?? [],
  });
};

// 备注开关切换
// const onRemarksToggle = (value: boolean) => {
//   receivingForm.remarksEnabled = value;
//   if (!value) {
//     receivingForm.remarks = ""; // 关闭时清空备注
//   }
// };
const toDetailForm = (item: any, status: string) => {
  Taro.navigateTo({
    url: `/subpackages/receiving/detailForm/index?id=${item.planId}&status=${status}&type=receive`,
  });
};

// 方法
// const toggleEditMode = () => {
//   isEditMode.value = !isEditMode.value;
//   if (isEditMode.value) {
//     Taro.showToast({
//       title: "进入编辑模式",
//       icon: "none",
//     });
//   }
// };

const confirmReceiving = async () => {
  // 验证表单
  // const hasEmptyQuantity = detailList.value.some(
  //   (item) => !item.actualQuantity
  // );
  // if (hasEmptyQuantity) {
  //   Taro.showToast({
  //     title: "请填写所有实际数量",
  //     icon: "none",
  //   });
  //   return;
  // }
  // 校验：判断所有 actualQuantity 是否填写
  if (!validateActualQuantities(shipmentDetail.value.details)) {
    Taro.showToast({
      title: "请填写所有实收数量",
      icon: "none",
    });
    return;
  }
  // 显示确认对话框
  const result = await Taro.showModal({
    title: "确认收货",
    content: "确定要提交收货信息吗？",
    confirmText: "确认",
    cancelText: "取消",
  });

  if (!result.confirm) {
    return;
  }

  confirmLoading.value = true;

  try {
    // 准备提交数据
    const submitData = {
      remark: receivingForm.remarks,
      remarkSwitch: receivingForm.remarksEnabled ? 1 : 0,
      receiveImages: images.value,
      deliveryPlanList: receivingFormStore.formItems,
      deliveryOrderNo: shipmentDetail.value.deliveryOrderNo,
    };

    const res = await confirmReceipt(submitData);
    console.log(res);
    console.log("提交数据:", submitData);

    // 模拟API调用
    // await new Promise((resolve) => setTimeout(resolve, 2000));

    Taro.showToast({
      title: "收货成功",
      icon: "none",
      duration: 2000,
    });

    receivingFormStore.clearFormItems();
    // 返回上一页
    setTimeout(() => {
      Taro.reLaunch({
        url: `/subpackages/receiving/receivingOperation/index`,
      });
    }, 400);
  } catch (error) {
    console.error("收货失败:", error);
    Taro.showToast({
      title: "收货失败，请重试",
      icon: "none",
    });
  } finally {
    confirmLoading.value = false;
  }
};

const validateActualQuantities = (details) => {
  console.log("validateActualQuantities", details);
  return details.every((detail) => {
    const quantity = detail.actualQuantity;
    return quantity !== null && quantity !== undefined && quantity !== "";
  });
};
// 获取收货单详情
const fetchShipmentDetail = async (deliveryOrderNo: string) => {
  try {
    loading.value = true;
    Taro.showLoading({ title: "加载中..." });

    const response = await queryDeliveryOrderDetail({ deliveryOrderNo });

    if (response && response.success && response.data) {
      const data = response.data;

      // 更新发货单详情数据
      shipmentDetail.value = {
        deliveryOrderNo: data.deliveryOrderNo || "",
        customerCreditCode: data.customerCreditCode || "",
        supplierCreditCode: data.supplierCreditCode || "",
        customerName: data.customerName || "",
        customerPrefix: data.customerPrefix || "",
        supplierName: data.supplierName || "",
        supplierPrefix: data.supplierPrefix || "",
        deliveryAddress: data.deliveryAddress || "",
        transportType: data.transportType || "",
        driverPhone: data.driverPhone || "",
        status: data.status || "",
        licensePlate: data.licensePlate || "",
        deliveryRemark: data.deliveryRemark || "",
        deliveryRemarkEnabled: !!data.deliveryRemarkEnabled || false,
        deliveryOperator: data.deliveryOperator || "",
        deliveryTime: data.deliveryTime || "",
        receiptQuantity: data.receiptQuantity || 0,
        receiptOperator: data.receiptOperator || "",
        receiptTime: data.receiptTime || "",
        receiptRemark: data.receiptRemark || "",
        receiptRemarkEnabled: data.receiptRemarkEnabled || false,
        categoryCount: data.categoryCount || 0,
        maxDeliveryTime: data.maxDeliveryTime || "",
        details: data.details || [],
        deliveryImages: data.deliveryImages || [],
        receiveImages: data.receiveImages || [],
        // detailRows: data?.details?.deliveryOrderHerbInfoVOList ? data?.details?.deliveryOrderHerbInfoVOList.length : 0,
      };
      // 更新收货图片
      receivingForm.receiveImages =
        (data.receiveImages &&
          data.receiveImages.map((item) => {
            return {
              url: item,
              name: item,
              status: "success",
              uid: item,
              type: "image/jpeg",
            };
          })) ||
        [];
      images.value = data.receiveImages || [];
      console.log("发货单详情加载成功:", data);
    } else {
      throw new Error(response?.message || "获取发货单详情失败");
    }

    Taro.hideLoading();
  } catch (error) {
    console.error("获取发货单详情失败:", error);
    Taro.hideLoading();
    Taro.showToast({
      title: "获取详情失败",
      icon: "none",
    });
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="less">
.shipment-detail-page {
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 200rpx;
  // 防止闪屏的关键优化
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  will-change: auto;
  // 防止背景重绘
  background-attachment: local;

  .header-info {
    border-radius: 16rpx;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
    padding: 16rpx 24rpx 1rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .info-content {
      flex: 1;
    }

    .info-item {
      margin-bottom: 16rpx;

      .label {
        color: rgba(29, 29, 29, 0.6);
        font-style: normal;
        font-weight: 400;
      }
      .label1 {
        color: #2f3133;
        font-size: 32rpx;
        font-style: normal;
        font-weight: 600;
      }

      .value {
        color: #1d1d1d;
        font-style: normal;
        font-weight: 400;
      }
      .value1 {
        flex: 1;
        overflow-x: auto;
        color: #2f3133;
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 50rpx;
        border-bottom: 1px solid #eef1f5;
      }
    }
  }

  .detail-section,
  .shipping-section {
    border-radius: 16rpx;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    margin-top: 20rpx;
  }

  .section-title {
    color: var(--, var(--, #1a43ad));
    font-style: normal;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;
    font-size: 32rpx;
    border-bottom: 1px solid #eef1f5;

    .collapse-arrow {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transform: rotate(0deg);
      transition: transform 0.2s ease;

      &.expanded {
        transform: rotate(180deg);
      }
    }
  }

  .collapse-content {
    background: white;
  }

  .shipping-info {
    .info-item {
      display: flex;
      padding: 24rpx;
      border-bottom: 1px solid #eef1f5;
      // 防止闪屏优化
      transform: translateZ(0);
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        color: #2f3133;
        min-width: 160rpx;
        margin-right: 20rpx;
      }

      .info-textarea {
        flex: 1;
        border-radius: 16rpx;
        background: #f5f7fa;
        height: 180rpx;
        // 防止闪屏优化
        transform: translateZ(0);
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        transition: none !important;
      }

      .info-value {
        color: #8d9094;
        flex: 1;
        line-height: 1.5;
        text-align: right;
      }

      .switch-container {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 16rpx;
        color: #8d9094;
        // 防止闪屏优化
        transform: translateZ(0);
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
      }

      // 开关项特殊优化
      &.switch-item {
        // 强制硬件加速
        transform: translate3d(0, 0, 0);
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        // 防止重排
        contain: layout style paint;
      }
    }
  }

  .image-label {
    color: #2f3133;
    min-width: 160rpx;
    margin-left: 24rpx;
    padding-top: 24rpx;
  }

  .image-gallery {
    padding: 24rpx;
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;

    .image-item {
      width: 128rpx;
      height: 128rpx;
      border-radius: 12rpx;
      overflow: hidden;

      .gallery-image {
        width: 100%;
        height: 100%;
        border-radius: 12rpx;
      }
    }

    .upload-button {
      width: 128rpx;
      height: 128rpx;
      border: 2rpx dashed #ddd;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fafafa;

      &:active {
        background-color: #f0f0f0;
      }
    }
  }

  .detail-item {
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 16rpx;
    transition: background-color 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .item-content {
      flex: 1;
    }

    &:active {
      background-color: #f5f5f5;
    }

    .item-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16rpx;

      .item-status {
        padding: 4rpx 12rpx;
        border-radius: 20rpx;
        font-size: 20rpx;
        margin-right: 16rpx;

        &.urgent {
          background-color: #faebeb;
          color: #fc474c;
        }
      }

      .plan-number {
        font-size: 28rpx;
        color: #2f3133;
        font-weight: 500;
        flex: 1;
      }

      .refresh-icon {
        padding: 8rpx;
      }
    }

    .item-details {
      .detail-row {
        display: flex;
        margin-bottom: 12rpx;

        .detail-col {
          flex: 1;
          display: flex;
          align-items: center;

          .label {
            font-size: 24rpx;
            color: #8d9094;
            margin-right: 8rpx;
            white-space: nowrap;
          }

          .value {
            font-size: 24rpx;
            color: #2f3133;
            flex: 1;
            word-break: break-all;

            word-wrap: break-word;
          }
        }
      }
    }
  }

  .confirm-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 60rpx 80rpx;
    background: white;
    border-top: 1rpx solid #eef1f5;
  }

  // NutUI 组件闪屏优化
  :deep(.nut-switch) {
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    transition: none !important;

    .nut-switch__button {
      transition: none !important;
      transform: translateZ(0);
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
    }
  }

  :deep(.nut-textarea) {
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;

    .nut-textarea__textarea {
      transition: none !important;
      transform: translateZ(0);
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
    }
  }

  // 页面整体优化
  .shipment-detail-page {
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    will-change: auto;
  }
}
</style>
