// 供货明细相关类型定义

export interface PlanInfo {
  planNo: string
  customerName: string
  enterpriseCode: string
  materialName: string
  planQuantity: string
  deliveryDate: string
  remarks?: string
}

export interface SupplyItem {
  id: string
  herbProductName: string
  quantity: string
  productionBatchNo: string
  identificationCode: string
  actualQuantity: string
  unitName: string
}

export interface SupplyDetailParams {
  planNo?: string
  customerName?: string
  enterpriseCode?: string
}

export interface ScanResult {
  result: string
  scanType: string
  charSet: string
  path: string
}

export interface SaveSupplyRequest {
  planNo: string
  supplyList: SupplyItem[]
}

export interface SaveSupplyResponse {
  success: boolean
  message: string
  data?: any
}

// 扫码后弹窗表单数据
export interface ScanFormData {
  materialName: string // 物料产品名称
  batchCode: string // 生产批号
  actualQuantity: string // 实发数量
}

// 表单验证规则
export interface FormValidationRule {
  required?: boolean
  message?: string
  validator?: (value: any) => boolean | string
}
