import { defineS<PERSON> } from 'pinia'
import { ref } from 'vue'
import Taro from '@tarojs/taro'
import { postAction, getAction } from "../../http/index";
import { request } from '../../http/request'

interface LoginParams {
  username: string
  password: string
}

interface UserInfo {
  id: string
  username: string
  nickname: string
  avatar: string
  companyName: string
  roleName: string
  entCode: string
  // Add other user info fields as needed
}

export const useUserStore = defineStore('user', () => {
  const userInfo = ref<UserInfo | null>(null)
  const token = ref<string>('')
  const menuList = ref<any>([])
  // Login function
  const login = async (params: LoginParams) => {
    try {
      // Replace with your actual API endpoint
      const response: any = await postAction('/acc/user/login', params)
      if (response.code !== 'SUCCESS') {
        Taro.showToast({
          title: response.msg,
          icon: "none",
        });
        return false
      }
      if (response.success) {
        token.value = response.data
        // userInfo.value = newUserInfo

        // Save to storage
        Taro.setStorageSync('token', token.value)
        // Taro.setStorageSync('userInfo', JSON.stringify(newUserInfo))
        getUserInfo()
        return true
      }
      return false
    } catch (error) {
      console.error('Login failed:', error)
      return false
    }
  }

  // Get user info
  const getUserInfo = async () => {
    try {
      const response: any = await request({
        url: '/acc/user/loginUser',
        method: 'GET',
        data: {},
        header: {
          'X-RD-Request-APIToken': `${token.value}`
        }
      })

      if (response.success) {
        userInfo.value = response.data
        Taro.setStorageSync('userInfo', JSON.stringify(response.data))
        getMenuList()
        return true
      }
      return false
    } catch (error) {
      console.error('Get user info failed:', error)
      return false
    }
  }
  const getMenuList = async () => {
    try {
      // Replace with your actual API endpoint
      const response: any = await getAction('/acc/user/menu')
      if (response.success) {
        menuList.value = response.data
        Taro.setStorageSync('menuList', JSON.stringify(response.data))
        return true
      }
      return false
    } catch (error) {
      console.error('Login failed:', error)
      return false
    }
  }
  // Logout
  const logout = () => {
    token.value = ''
    userInfo.value = null
    menuList.value = []
    Taro.removeStorageSync('token')
    Taro.removeStorageSync('userInfo')
    Taro.removeStorageSync('menuList')
  }

  // Initialize from storage
  const initFromStorage = () => {
    const storedToken = Taro.getStorageSync('token')
    const storedUserInfo = Taro.getStorageSync('userInfo')
    const storedMenuList = Taro.getStorageSync('menuList')
    if (storedMenuList) {
      menuList.value = JSON.parse(storedMenuList)
    }
    if (storedToken) {
      token.value = storedToken
    }

    if (storedUserInfo) {
      userInfo.value = JSON.parse(storedUserInfo)
    }
  }

  return {
    userInfo,
    token,
    login,
    getUserInfo,
    logout,
    initFromStorage,
    menuList
  }
})
