<template>
  <view class="supply-detail-page">
    <!-- 页面头部信息 -->
    <view class="header-info">
      <view class="info-content">
        <view class="info-item">
          <view class="label1">到货计划编号</view>
          <view class="value1">{{ planInfo.planCode }}</view>
        </view>
        <view class="info-item">
          <text class="label">客户名称：</text>
          <text class="value">{{ planInfo.customerName }}</text>
        </view>
        <view class="info-item">
          <text class="label">企业前缀：</text>
          <text class="value">{{ planInfo.enterpriseCode }}</text>
        </view>
      </view>
      <view>
        <image
          style="width: 100rpx; height: 120rpx"
          src="../../../images/xq.png"
          mode="aspectFit"
        />
      </view>
    </view>

    <!-- 计划信息卡片 -->
    <view class="plan-info-section">
      <view class="section-title">计划信息</view>
      <view class="plan-info">
        <view class="info-item">
          <text class="info-label">物料名称（客）</text>
          <text class="info-value">{{ planInfo.materialName }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">计划数量</text>
          <text class="info-value"
            >{{ planInfo.planQuantity }}{{ planInfo.unitName }}</text
          >
        </view>
        <view class="info-item">
          <text class="info-label">计划交货时间</text>
          <text class="info-value">{{ planInfo.planDeliveryTime }}</text>
        </view>
        <view v-if="planInfo.remarkSwitch == 1" class="info-item">
          <text class="info-label">计划备注</text>
          <text class="info-value">{{ "对方备注给你" }}</text>
        </view>
        <view v-if="planInfo.remarkSwitch == 1" class="info-item textarea-item">
          <view class="info-textarea">
            <text class="textarea-placeholder">{{
              planInfo.remark || "对方备注给你"
            }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 供货信息列表 -->
    <view class="supply-info-section">
      <view class="section-title">
        <text>供货信息</text>
        <view class="scan-button" @click="handleScan">
          <image
            style="width: 32rpx; height: 32rpx"
            src="../../../images/sm.png"
            mode="aspectFit"
          />
        </view>
      </view>

      <scroll-view scroll-y class="supply-list" @scrolltolower="loadMore">
        <nut-swipe
          v-for="(item, index) in supplyList.filter(
            (item) => item.identificationCode
          )"
          :key="index"
          :name="index.toString()"
          @open="onSwipeOpen"
          @close="onSwipeClose"
        >
          <view
            style="
              display: flex;
              align-items: center;
              justify-content: space-between;
            "
          >
            <view class="supply-item">
              <view style="display: flex; align-items: center">
                <view class="item-status urgent" v-if="!item.actualQuantity"
                  >未填报</view
                >
                <view class="item-header">
                  <text class="material-name">{{ item.herbProductName }}</text>
                  <text v-if="item.actualQuantity" class="quantity"
                    >{{ item.actualQuantity }}{{ item.unitName }}</text
                  >
                </view>
              </view>
              <view class="item-details">
                <text class="batch-code">{{ item.productionBatchNo }}</text>
              </view>
              <view class="item-details">
                <text class="supplier-code">{{ item.identificationCode }}</text>
              </view>
            </view>
            <view @click="handleEdit(item)">
              <image
                style="width: 32rpx; height: 32rpx; margin: 0 16rpx"
                src="../../../images/edit.png"
                mode="aspectFit"
              >
              </image>
            </view>
          </view>
          <template #right>
            <view class="delete-action" @click="handleDelete(index)">
              删除
            </view>
          </template>
        </nut-swipe>

        <!-- 加载更多提示 -->
        <view v-if="loading" class="loading-more">
          <view class="loading-spinner">⏳</view>
          <text>加载中...</text>
        </view>

        <view v-if="!hasMore && supplyList.length > 0" class="no-more">
          <text>没有更多数据了</text>
        </view>
      </scroll-view>
    </view>

    <!-- 底部保存按钮 -->
    <view class="bottom-actions">
      <nut-button
        type="success"
        block
        class="save-btn"
        @click="handleSave"
        :loading="saving"
      >
        保存信息
      </nut-button>
    </view>

    <!-- 扫码成功后的信息弹窗 -->
    <nut-popup
      v-model:visible="showScanForm"
      position="bottom"
      :style="{ height: 'auto', minHeight: '50%' }"
      closeable
      close-icon-position="top-right"
      @close="closeScanForm"
    >
      <view class="scan-form-container">
        <view class="form-header">
          <text class="form-title">{{ planInfo.planNo }}</text>
        </view>

        <view class="scan-form">
          <view class="form-item">
            <text class="item-label">药材产品名称</text>
            <text class="item-value">{{ scanFormData.herbProductName }}</text>
          </view>

          <view class="form-item">
            <text class="item-label">生产批号</text>
            <text class="item-value">{{ scanFormData.productionBatchNo }}</text>
          </view>

          <view class="form-item input-item">
            <text class="item-label required">实发数量</text>
            <view class="input-wrapper">
              <nut-input
                v-model="scanFormData.actualQuantity"
                placeholder="请输入"
                type="text"
                clearable
                @input="onQuantityInput"
                class="quantity-input"
              />
              <view class="input-arrow">
                <IconFont name="right" size="16" color="#31373D"></IconFont>
              </view>
            </view>
          </view>
        </view>

        <view class="form-actions">
          <nut-button
            type="success"
            block
            class="confirm-btn"
            @click="confirmScanForm"
            :loading="formSubmitting"
          >
            保存信息
          </nut-button>
        </view>
      </view>
    </nut-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import Taro, { useRouter } from "@tarojs/taro";
import type { SupplyItem } from "@/types/supply";
import { IconFont } from "@nutui/icons-vue-taro";
import { useShipmentStore } from "@/store/modules/shipment";
import { queryDeliveryPlanDetail, queryHerbCode } from "@/api/shipment";

const shipmentStore = useShipmentStore();
// 路由参数
const router = useRouter();
const planNo = ref<string>("");

// 响应式数据
const planInfo = ref<any>({});

const supplyList = ref<SupplyItem[]>([]);

const loading = ref(false);
const hasMore = ref(true);
const saving = ref(false);

// 扫码弹窗相关数据
const showScanForm = ref(false);
const formSubmitting = ref(false);
const scanFormData = ref<any>({});
const handleEdit = (item: any) => {
  scanFormData.value = { ...item };
  showScanForm.value = true;
};

// 方法

// 数量输入验证
const validateQuantity = (value: string) => {
  if (!value || value.trim() === "") {
    return false;
  }
  // 验证是否为有效的数量格式（数字+单位）
  const quantityRegex = /^\d+(\.\d+)?(kg|g|t|吨|公斤|克)?$/i;
  return quantityRegex.test(value.trim());
};

// 数量输入处理
const onQuantityInput = (value: string) => {
  // 可以在这里添加实时验证逻辑
  scanFormData.value.actualQuantity = value;
};

// 关闭扫码表单弹窗
const closeScanForm = () => {
  showScanForm.value = false;
  // 重置表单数据
  scanFormData.value = {
    herbProductName: "",
    productionBatchNo: "",
    actualQuantity: "",
  };
};

// 确认扫码表单
const confirmScanForm = async () => {
  // 表单验证
  if (!scanFormData.value.actualQuantity.trim()) {
    Taro.showToast({
      title: "请输入实发数量",
      icon: "none",
    });
    return;
  }

  if (!validateQuantity(scanFormData.value.actualQuantity)) {
    Taro.showToast({
      title: "请输入有效的数量格式",
      icon: "none",
    });
    return;
  }

  formSubmitting.value = true;

  try {
    // 创建新的供货项目
    const newItem: any = {
      ...scanFormData.value,
    };
    console.log(newItem, "newItem");

    // 添加到供货列表
    const index = supplyList.value.findIndex(
      (item) => item.identificationCode === newItem.identificationCode
    );
    console.log(index, "index");

    if (index !== -1) {
      supplyList.value.splice(index, 1, newItem);
    } else {
      supplyList.value.push(newItem);
    }

    // 显示成功提示
    // Taro.showToast({
    //   title: "添加成功",
    //   icon: "none",
    // });

    // 关闭弹窗
    closeScanForm();
  } catch (error) {
    console.error("添加供货信息失败:", error);
    Taro.showToast({
      title: "添加失败",
      icon: "none",
    });
  } finally {
    formSubmitting.value = false;
  }
};

const loadMore = () => {
  // if (hasMore.value && !loading.value) {
  //   loading.value = true;
  //   // 模拟加载更多数据
  //   setTimeout(() => {
  //     // 这里可以添加更多数据
  //     loading.value = false;
  //     hasMore.value = false; // 模拟没有更多数据
  //   }, 1000);
  // }
};

const onSwipeOpen = (name: number) => {
  console.log("打开滑动", name);
};

const onSwipeClose = (name: number) => {
  console.log("关闭滑动", name);
};

const handleDelete = (index: number) => {
  Taro.showModal({
    title: "确认删除",
    content: "确定要删除这条供货信息吗？",
    success: (res) => {
      if (res.confirm) {
        supplyList.value.splice(index, 1);
        Taro.showToast({
          title: "删除成功",
          icon: "none",
        });
      }
    },
  });
};

const handleScan = () => {
  Taro.scanCode({
    success: (res) => {
      console.log("扫码结果", res);
      if (res.result.includes("zysbpt")) {
        // 解析扫码结果，这里模拟从二维码中解析出物料信息
        // 实际项目中需要根据具体的二维码格式进行解析
        const idisCode = res.result.slice(6);
        const index = supplyList.value.findIndex(
          (item: any) => item.idisCode === idisCode
        );
        if (index !== -1) {
          Taro.showToast({
            title: "该药材已添加",
            icon: "none",
          });
          return;
        }
        queryHerbCode({ idisCode }).then((res) => {
          if (res.success) {
            // 填充表单数据
            scanFormData.value = {
              id: res.data.id,
              deliveryOrderNo: planInfo.value.deliveryOrderNo,
              actualQuantity: "", // 实发数量需要用户手动输入
              herbProductCode: res.data.productNo,
              herbProductName: res.data.productName,
              productionBatchNo: res.data.batchNo,
              identificationCode: res.data.idisCode,
              planNo: planInfo.value.planNo,
              unit: planInfo.value.unit,
              unitName: planInfo.value.unitName,
            };

            // 显示弹窗表单
            showScanForm.value = true;

            Taro.showToast({
              title: "扫码成功",
              icon: "none",
              duration: 1000,
            });
          } else {
            Taro.showToast({
              title: "扫码失败",
              icon: "none",
            });
          }
        });
      } else {
        Taro.showToast({
          title: "二维码不正确",
          icon: "none",
        });
      }
    },
    fail: (err) => {
      console.error("扫码失败", err);
      // Taro.showToast({
      //   title: "扫码失败",
      //   icon: "none",
      // });
    },
  });
};

const handleSave = () => {
  if (supplyList.value.length === 0) {
    Taro.showToast({
      title: "请添加供货信息",
      icon: "none",
    });
    return;
  }

  saving.value = true;

  shipmentStore.detailList.forEach((item) => {
    if (item.planNo === planNo.value) {
      item.deliveryOrderHerbInfoVOList = supplyList.value;
    }
  });
  saving.value = false;
  Taro.showToast({
    title: "保存成功",
    icon: "none",
  });

  // 保存成功后返回上一页
  setTimeout(() => {
    Taro.navigateBack();
  }, 400);
};

// 生命周期
onMounted(() => {
  // 从路由参数获取计划信息
  const params = router.params as any;
  if (params.planNo) {
    planNo.value = decodeURIComponent(params.planNo);

    supplyList.value = [
      ...shipmentStore.detailList.filter(
        (item) => item.planNo === planNo.value
      )[0].deliveryOrderHerbInfoVOList,
    ];
    console.log(supplyList.value);
    queryDeliveryPlanDetail({ planNo: planNo.value }).then((res) => {
      planInfo.value = res.data;
      if (params.customerName) {
        planInfo.value.customerName = decodeURIComponent(params.customerName);
      }
      if (params.enterpriseCode) {
        planInfo.value.enterpriseCode = decodeURIComponent(
          params.enterpriseCode
        );
      }
      if (params.deliveryOrderNo) {
        planInfo.value.deliveryOrderNo = decodeURIComponent(
          params.deliveryOrderNo
        );
      }
    });
  }
  // tagDetailDateListApi({
  //     syncStatus: 20,
  //     filterIdisCode: [],
  //   }).then((res) => {
  //     if (res.success) {
  //       supplyList.value = res.data.list.map((item) => {
  //         return {
  //           id: item.id,
  //           deliveryOrderNo: planInfo.value.deliveryOrderNo,
  //           actualQuantity: "", // 实发数量需要用户手动输入
  //           herbProductCode: item.productNo,
  //           herbProductName: item.productName,
  //           productionBatchNo: item.batchNo,
  //           identificationCode: item.idisCode,
  //           planNo: planInfo.value.planNo,
  //           unit: planInfo.value.unit,
  //           unitName: planInfo.value.unitName,
  //         };
  //       });
  //     }
  //   });
});
</script>

<style lang="scss">
.supply-detail-page {
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 200rpx; // 为底部固定按钮留出空间

  .header-info {
    border-radius: 16rpx;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
    padding: 16rpx 24rpx 1rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .info-content {
      flex: 1;
    }

    .info-item {
      margin-bottom: 16rpx;

      .label {
        color: rgba(29, 29, 29, 0.6);
        font-style: normal;
        font-weight: 400;
      }

      .label1 {
        color: #2f3133;
        font-size: 32rpx;
        font-style: normal;
        font-weight: 600;
      }

      .value {
        color: #1d1d1d;
        font-style: normal;
        font-weight: 400;
      }

      .value1 {
        flex: 1;
        overflow-x: auto;
        color: #2f3133;
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 50rpx;
        border-bottom: 1px solid #eef1f5;
      }
    }
  }

  .plan-info-section {
    border-radius: 16rpx;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    margin-bottom: 20rpx;

    .section-title {
      color: #1a43ad;
      font-style: normal;
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx;
      font-size: 32rpx;
      border-bottom: 1px solid #eef1f5;
    }

    .plan-info {
      .info-item {
        display: flex;
        padding: 24rpx;
        border-bottom: 1px solid #eef1f5;

        &:last-child {
          border-bottom: none;
        }

        &.textarea-item {
          flex-direction: column;
          align-items: stretch;
        }

        .info-label {
          color: #2f3133;
          min-width: 160rpx;
          margin-right: 20rpx;
        }

        .info-value {
          color: #8d9094;
          flex: 1;
          line-height: 1.5;
          text-align: right;
        }

        .info-textarea {
          margin-top: 16rpx;
          border-radius: 16rpx;
          background: #f5f7fa;
          min-height: 120rpx;
          padding: 16rpx;
          display: flex;
          align-items: flex-start;

          .textarea-placeholder {
            color: #8d9094;
            font-size: 24rpx;
            line-height: 1.5;
          }
        }
      }
    }
  }

  .supply-info-section {
    border-radius: 16rpx;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    margin-bottom: 20rpx;

    .section-title {
      color: #1a43ad;
      font-style: normal;
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx;
      font-size: 32rpx;
      border-bottom: 1px solid #eef1f5;

      .scan-button {
        width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .supply-list {
    .supply-item {
      flex: 1;
      padding: 24rpx;
      border-bottom: 1px solid #eef1f5;
      display: flex;
      flex-direction: column;
      position: relative;
      .item-status {
        padding: 4rpx 12rpx;
        border-radius: 20rpx;
        font-size: 20rpx;
        margin-right: 16rpx;

        &.urgent {
          background-color: #faebeb;
          color: #fc474c;
        }
      }
      .item-header {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .material-name {
          font-size: 28rpx;
          font-weight: 600;
          color: #2f3133;
          flex: 1;
        }

        .quantity {
          font-size: 28rpx;
          font-weight: 600;
          color: #2f3133;
        }
      }

      .item-details {
        color: #8d9094;
        margin-bottom: 8rpx;

        .batch-code,
        .supplier-code {
          display: inline-block;
          line-height: 1.4;
          word-break: break-all;

          word-wrap: break-word;
        }
      }
    }

    .delete-action {
      background: #fc474c;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 64rpx;
      height: 100%;
      font-size: 24rpx;
      font-weight: 400;
    }
  }

  .loading-more,
  .no-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 32rpx;
    color: #999;
    font-size: 28rpx;

    .loading-spinner {
      margin-right: 16rpx;
      font-size: 32rpx;
      animation: spin 1s linear infinite;
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    padding: 24rpx 32rpx;
    padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
    border-top: 1rpx solid #f0f0f0;
    box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.05);

    .save-btn {
      height: 88rpx;
      border-radius: 44rpx;
      font-size: 32rpx;
      font-weight: 500;
      background: linear-gradient(135deg, #0478ff 0%, #0056cc 100%);
      border: none;
    }
  }

  // 扫码弹窗样式
  .scan-form-container {
    padding: 40rpx 32rpx 32rpx;
    background: #fff;

    .form-header {
      text-align: left;
      padding-bottom: 20rpx;
      border-bottom: 1rpx solid #eef1f5;

      .form-title {
        font-size: 36rpx;
        font-weight: 600;
        color: #000;
      }
    }

    .scan-form {
      margin-bottom: 40rpx;

      .form-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 0;
        border-bottom: 1rpx solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        &.input-item {
          align-items: center;
        }

        .item-label {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;

          &.required::before {
            content: "*";
            color: #ff4d4f;
            margin-right: 4rpx;
          }
        }

        .item-value {
          font-size: 28rpx;
          color: #999;
          text-align: right;
        }

        .input-wrapper {
          display: flex;
          align-items: center;
          flex: 1;
          margin-left: 20rpx;

          .quantity-input {
            flex: 1;

            :deep(.nut-input__inner) {
              border: none;
              padding: 0;
              text-align: right;
              font-size: 28rpx;
              color: #333;

              &::placeholder {
                color: #999;
              }
            }
          }

          .input-arrow {
            margin-left: 16rpx;
            color: #999;
            font-size: 24rpx;
          }
        }
      }
    }

    .form-actions {
      .confirm-btn {
        height: 88rpx;
        border-radius: 44rpx;
        font-size: 32rpx;
        font-weight: 500;
        background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
        border: none;

        &:disabled {
          background: #d9d9d9;
          opacity: 0.6;
        }
      }
    }
  }

  // NutUI组件样式覆盖

  .nut-popup {
    border-radius: 40rpx 40rpx 0 0 !important;

    .nut-popup__close-icon {
      top: 24rpx;
      right: 24rpx;
      font-size: 32rpx;
      color: #000;
    }
  }
  .nut-input__inner::placeholder {
    text-align: right !important;
  }
}
</style>
