import { post, get, put, del } from '../http/request'

// 发货单列表查询参数
export interface ShipmentListParams {
  page: number
  pageSize: number
  status: 'pending' | 'confirmed' // 待确认/已确认
}

// 发货单项目接口
export interface ShipmentItem {
  deliveryOrderNo?: string // 发货单号
  id?: number // 主键ID
  materialCode?: string // 物料编码
  materialName?: string // 物料名称
  planDeliveryTime?: string // 计划交付时间
  planNo?: string // 计划编号
  planQuantity?: number // 计划数量
  unit?: string // 单位
  unitName?: string // 单位名称
  // 保留原有字段以兼容现有代码
  shipmentNo?: string // 发货单号（兼容字段）
  customerName?: string // 客户名称（兼容字段）
  address?: string // 收货地址（兼容字段）
  productCount?: number // 品类数（兼容字段）
  daysRemaining?: number // 距最近交货时间（兼容字段）
}

// 获取发货单列表
export const getShipmentList = (data: any) => {
  return post('/acc/api/delivery/planCooperation/wx/order/page', data)
}
// 获取发货单详情
export const queryDeliveryOrderDetail = (data: any) => {
  return get(`/acc/api/delivery/planCooperation/order/detail`, data)
}

// 查询计划
export const queryDeliveryPlanAll = (data: any) => {
  return post(`/acc/api/delivery/planCooperation/selected/list`, data)
}
// 供货计划协同
export const queryCooperationLList = (data: any) => {
  return post(`/acc/api/delivery/planCooperation/page`, data)
}
// 根据计划编号查询计划详情
export const queryDeliveryPlanDetail = (data: any) => {
  return get(`/acc/api/delivery/planCooperation/plan/detail`, data)
}
// 药材code
export const queryHerbCode = (data: any) => {
  return post(`/acc/api/code/getByIdisCode`, data)
}
// 标识明细
export const tagDetailDateListApi = (data: any) => {
  return post(`/acc/api/code/pageList`, data)
}
// 更新发货单
export const updateDeliveryOrderApi = (data: any) => {
  return post(`/acc/api/delivery/planCooperation/order/update`, data)
}
// 发货单拿状态
export const getDeliveryOrderStatusApi = (data: any) => {
  return get(`/acc/api/delivery/planCooperation/order/deliveryStatus`, data)
}
