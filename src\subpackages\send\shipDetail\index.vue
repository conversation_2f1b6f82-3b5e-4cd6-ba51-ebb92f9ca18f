<template>
  <view class="ship-detail-page">
    <!-- 发货单详情头部 -->
    <view class="header-info">
      <view class="info-content">
        <view class="info-item">
          <view class="label1">发货单号</view>
          <view class="value1">{{ shipmentDetail.deliveryOrderNo }}</view>
        </view>
        <view class="info-item">
          <text class="label">客户名称：</text>
          <text class="value">{{ shipmentDetail.customerName }}</text>
        </view>
        <view class="info-item">
          <text class="label">企业前缀：</text>
          <text class="value">{{ shipmentDetail.customerPrefix }}</text>
        </view>
      </view>
      <view>
        <image
          style="width: 100rpx; height: 120rpx"
          src="../../../images/xq.png"
          mode="aspectFit"
        >
        </image>
      </view>
    </view>

    <!-- 明细信息 -->
    <view class="detail-section">
      <view class="section-title">明细信息</view>
      <view class="detail-list">
        <view
          class="detail-item"
          v-for="(item, index) in shipmentDetail.details"
          :key="index"
        >
          <view class="item-content">
            <view class="item-row">
              <text class="item-label">计划编号：</text>
              <text class="item-value">{{ item.planNo || "-" }}</text>
            </view>
            <view class="item-row">
              <text class="item-label">物料名称：</text>
              <text class="item-value">{{ item.materialName || "-" }}</text>
            </view>
            <nut-row style="margin-bottom: 10rpx">
              <nut-col :span="13">
                <view class="item-row">
                  <text class="item-label">明细行数：</text>
                  <text class="item-value">{{
                    item.deliveryOrderHerbInfoVOList.length || 0
                  }}</text>
                </view>
              </nut-col>
              <nut-col :span="11">
                <view class="item-row">
                  <text class="item-label">计划数量：</text>
                  <text class="item-value"
                    >{{ item.planQuantity }}{{ item.unitName }}</text
                  >
                </view>
              </nut-col>
            </nut-row>
            <nut-row>
              <nut-col :span="13">
                <view class="item-row">
                  <text class="item-label">计划交货时间：</text>
                  <text class="item-value">{{ item.planDeliveryTime }}</text>
                </view>
              </nut-col>
              <nut-col :span="11">
                <view class="item-row">
                  <text class="item-label">实发数量：</text>
                  <text class="item-value"
                    >{{ calculateActualQuantity(item)
                    }}{{ item.unitName }}</text
                  >
                </view>
              </nut-col>
            </nut-row>
          </view>
          <!-- 111111 -->
          <view @click="toDetailForm(item, '3')">
            <image
              style="width: 32rpx; height: 32rpx"
              src="../../../images/info.png"
              mode="aspectFit"
            >
            </image>
          </view>
        </view>
      </view>
    </view>

    <!-- 发货信息 -->
    <view class="detail-section">
      <view class="section-title">发货信息</view>
      <view class="shipping-info">
        <view class="info-item">
          <text class="info-label">收货地址</text>
          <text class="info-value">{{
            shipmentDetail.deliveryAddress || "-"
          }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">运输类型</text>
          <text class="info-value">{{
            shipmentDetail.transportType || "-"
          }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">司机电话</text>
          <text class="info-value">{{
            shipmentDetail.driverPhone || "-"
          }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">车牌号</text>
          <text class="info-value">{{
            shipmentDetail.licensePlate || "-"
          }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">发货备注</text>
          <text class="info-value">{{ "备注给对方" }}</text>
        </view>
        <view class="info-item">
          <text class="info-textarea">{{ shipmentDetail.deliveryRemark }}</text>
        </view>

        <!-- 发货图片展示 -->
        <view
          v-if="
            shipmentDetail.deliveryImages &&
            shipmentDetail.deliveryImages.length > 0
          "
        >
          <view class="image-label">图片</view>
          <view class="image-gallery">
            <view
              class="image-item"
              v-for="(image, index) in shipmentDetail.deliveryImages"
              :key="`delivery-${index}`"
              @tap="previewImage(image)"
            >
              <image :src="image" mode="aspectFill" class="gallery-image" />
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="detail-section" v-if="shipmentDetail.status === '3'">
      <view class="section-title">收货信息</view>

      <view class="shipping-info">
        <view class="info-item">
          <text class="info-label">收货时间</text>
          <text class="info-value">{{ shipmentDetail.receiptTime }}</text>
        </view>
        <view class="info-item" v-if="shipmentDetail.receiptRemarkEnabled">
          <text class="info-label">收货备注</text>
        </view>
        <view class="info-item" v-if="shipmentDetail.receiptRemarkEnabled">
          <text class="info-textarea">{{
            shipmentDetail.receiptRemark ?? ""
          }}</text>
        </view>
      </view>

      <!-- 图片区域 -->
      <view class="image-label">图片</view>
      <view class="image-gallery">
        <view
          class="image-item"
          v-for="(image, index) in shipmentDetail.receiveImages"
          :key="index"
          @click="previewImage(image)"
        >
          <image :src="image" mode="aspectFill" class="gallery-image" />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import Taro, { useRouter } from "@tarojs/taro";
import type { ShipmentDetail } from "@/types/shipment";
import { queryDeliveryOrderDetail } from "@/api/shipment";

// 路由参数
const router = useRouter();

// 发货单详情数据
const shipmentDetail = ref<ShipmentDetail>({
  deliveryOrderNo: "",
  customerName: "",
  deliveryAddress: "",
  transportType: "",
  driverPhone: "",
  status: "",
  licensePlate: "",
  details: [],
});

// 页面加载状态
const loading = ref(false);

onMounted(() => {
  // 获取路由参数
  const { deliveryOrderNo } = router.params;
  if (deliveryOrderNo) {
    fetchShipmentDetail(deliveryOrderNo);
  }
});
const toDetailForm = (item: any, status: string) => {
  Taro.navigateTo({
    url: `/subpackages/receiving/detailForm/index?id=${item.planId}&status=${status}&type=send`,
  });
};
// 计算实发数量总和
const calculateActualQuantity = (item: any) => {
  if (
    !item.deliveryOrderHerbInfoVOList ||
    !Array.isArray(item.deliveryOrderHerbInfoVOList)
  ) {
    return 0;
  }

  return item.deliveryOrderHerbInfoVOList.reduce(
    (total: number, herbInfo: any) => {
      const actualQuantity = parseFloat(herbInfo.actualQuantity) || 0;
      return total + actualQuantity;
    },
    0
  );
};
// 获取发货单详情
const fetchShipmentDetail = async (deliveryOrderNo: string) => {
  try {
    loading.value = true;
    Taro.showLoading({ title: "加载中..." });

    const response = await queryDeliveryOrderDetail({ deliveryOrderNo });

    if (response && response.success && response.data) {
      const data = response.data;

      // 更新发货单详情数据
      shipmentDetail.value = {
        deliveryOrderNo: data.deliveryOrderNo || "",
        customerCreditCode: data.customerCreditCode || "",
        supplierCreditCode: data.supplierCreditCode || "",
        customerName: data.customerName || "",
        customerPrefix: data.customerPrefix || "",
        supplierName: data.supplierName || "",
        supplierPrefix: data.supplierPrefix || "",
        deliveryAddress: data.deliveryAddress || "",
        transportType: data.transportType || "",
        driverPhone: data.driverPhone || "",
        status: data.status || "",
        licensePlate: data.licensePlate || "",
        deliveryRemark: data.deliveryRemark || "",
        deliveryRemarkEnabled: data.deliveryRemarkEnabled || false,
        deliveryOperator: data.deliveryOperator || "",
        deliveryTime: data.deliveryTime || "",
        receiptQuantity: data.receiptQuantity || 0,
        receiptOperator: data.receiptOperator || "",
        receiptTime: data.receiptTime || "",
        receiptRemark: data.receiptRemark || "",
        receiptRemarkEnabled: data.receiptRemarkEnabled || false,
        categoryCount: data.categoryCount || 0,
        maxDeliveryTime: data.maxDeliveryTime || "",
        details: data.details || [],
        deliveryImages: data.deliveryImages || [],
        receiveImages: data.receiveImages || [],
        createTime: data.createTime || "",
        updateTime: data.updateTime || "",
      };

      console.log("发货单详情加载成功:", data);
    } else {
      throw new Error(response?.message || "获取发货单详情失败");
    }

    Taro.hideLoading();
  } catch (error) {
    console.error("获取发货单详情失败:", error);
    Taro.hideLoading();
    Taro.showToast({
      title: "获取详情失败",
      icon: "none",
    });
  } finally {
    loading.value = false;
  }
};

// 预览图片
const previewImage = (current: string) => {
  const allImages = [
    ...(shipmentDetail.value.deliveryImages || []),
    ...(shipmentDetail.value.receiveImages || []),
  ];

  Taro.previewImage({
    current,
    urls: allImages,
  });
};

// 格式化状态显示
const getStatusText = (status: string) => {
  switch (status) {
    case "1":
      return "待确认发货";
    case "2":
      return "已确认发货";
    case "3":
      return "对方已收货";
    default:
      return "未知状态";
  }
};

// 格式化日期显示
const formatDate = (dateStr: string) => {
  if (!dateStr) return "";
  try {
    const date = new Date(dateStr);
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  } catch (error) {
    return dateStr;
  }
};
</script>

<style lang="less">
.ship-detail-page {
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 40rpx;

  .header-info {
    border-radius: 16rpx;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
    padding: 16rpx 24rpx 1rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .info-content {
      flex: 1;
    }
    .info-item {
      margin-bottom: 16rpx;

      .label {
        color: rgba(29, 29, 29, 0.6);
        font-style: normal;
        font-weight: 400;
      }
      .label1 {
        color: #2f3133;
        font-size: 32rpx;
        font-style: normal;
        font-weight: 600;
      }

      .value {
        color: #1d1d1d;
        font-style: normal;
        font-weight: 400;
      }
      .value1 {
        flex: 1;
        overflow-x: auto;
        color: #2f3133;
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 50rpx;
        border-bottom: 1px solid #eef1f5;
      }
    }
  }

  .detail-section,
  .shipping-section {
    border-radius: 16rpx;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    margin-top: 20rpx;
  }

  .section-title {
    color: var(--, var(--, #1a43ad));
    font-style: normal;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;
    font-size: 32rpx;
    border-bottom: 1px solid #eef1f5;
  }

  .detail-list {
    .detail-item {
      padding: 24rpx;
      border-bottom: 1px solid #eef1f5;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;

      &:last-child {
        border-bottom: none;
      }

      .item-content {
        flex: 1;
        .item-row {
          display: flex;
          margin-bottom: 12rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .item-label {
            color: #8d9094;
            min-width: 180rpx;
          }

          .item-value {
            color: #2f3133;
            flex: 1;
          }
        }
      }
    }
  }

  .shipping-info {
    .info-item {
      display: flex;
      padding: 24rpx;
      border-bottom: 1px solid #eef1f5;

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        color: #2f3133;
        min-width: 160rpx;
        margin-right: 20rpx;
      }
      .info-textarea {
        flex: 1;
        border-radius: 16rpx;
        background: #f5f7fa;
        height: 180rpx;
      }

      .info-value {
        color: #8d9094;
        flex: 1;
        line-height: 1.5;
        text-align: right;
      }
    }
  }
  .image-label {
    color: #2f3133;
    min-width: 160rpx;
    margin-left: 24rpx;
    padding-top: 24rpx;
  }
  .image-gallery {
    padding: 24rpx;
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;

    .image-item {
      width: 128rpx;
      height: 128rpx;
      border-radius: 12rpx;
      overflow: hidden;

      .gallery-image {
        width: 100%;
        height: 100%;
        border-radius: 12rpx;
      }
    }
  }
}
</style>
