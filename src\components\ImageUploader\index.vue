<template>
  <view class="image-uploader">
    <nut-uploader
      v-model:file-list="fileList"
      :maximum="maximum"
      :maximize="maxSizeBytes"
      multiple
      capture
      :url="uploadUrl"
      :headers="uploadHeaders"
      name="file"
      :preview-type="'picture'"
      :preview="true"
      @preview="uploadPreview"
      @oversize="onOversize"
      @delete="onDelete"
      @start="onStart"
      @progress="onProgress"
      @success="onSuccess"
      @failure="onFailure"
      @change="onChange"
    >
      <template #upload>
        <view class="upload-button" v-if="fileList.length < maximum">
          <image
            style="width: 128rpx; height: 128rpx"
            :src="uploadIcon"
            mode="aspectFit"
          />
        </view>
      </template>
    </nut-uploader>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import Taro from "@tarojs/taro";

// Props 定义
interface Props {
  modelValue?: any[]; // v-model 绑定的文件列表
  maximum?: number; // 最大上传数量
  maxSize?: number; // 最大文件大小（MB）
  uploadIcon?: string; // 上传按钮图标
  disabled?: boolean; // 是否禁用
  allowedTypes?: string[]; // 允许的文件类型
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  maximum: 3,
  maxSize: 4,
  uploadIcon: "../../../images/tup.png",
  disabled: false,
  allowedTypes: () => [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/gif",
    "image/webp",
  ],
});

// Emits 定义
interface Emits {
  (e: "update:modelValue", value: any[]): void;
  (e: "upload-success", data: any): void;
  (e: "upload-error", error: any): void;
  (e: "delete", file: any): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const fileList = ref<any[]>([]);
const uploadUrl = ref("");
const uploadHeaders = ref({});

// 计算属性
const maxSizeBytes = computed(() => props.maxSize * 1024 * 1024);

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    fileList.value = newValue || [];
  },
  { immediate: true, deep: true }
);

// 监听 fileList 变化，同步到父组件
watch(
  fileList,
  (newValue) => {
    emit("update:modelValue", newValue);
  },
  { deep: true }
);

// 事件处理函数
// 上传前的验证
// const beforeUpload = (file: any) => {
//   console.log("before-upload 触发", file);

//   // 检查文件类型
//   const isValidType = props.allowedTypes.includes(file.type);

//   if (!isValidType) {
//     const typeNames = props.allowedTypes
//       .map((type) => type.split("/")[1].toUpperCase())
//       .join("、");
//     Taro.showToast({
//       title: `只支持上传 ${typeNames} 格式的图片`,
//       icon: "none",
//       duration: 3000,
//     });
//     return false;
//   }

//   // 检查文件大小
//   const maxSize = props.maxSize * 1024 * 1024;
//   const isValidSize = file.size <= maxSize;

//   if (!isValidSize) {
//     Taro.showToast({
//       title: `图片大小不能超过${props.maxSize}MB`,
//       icon: "none",
//       duration: 3000,
//     });
//     return false;
//   }

//   return true;
// };

const onOversize = (files: any[]) => {
  console.log("oversize 触发 文件大小不能超过", props.maxSize, "MB", files);
  Taro.showToast({
    title: `图片大小不能超过${props.maxSize}MB`,
    icon: "none",
  });
};

const onDelete = (file: any, newFileList: any[]) => {
  console.log("删除图片", file, newFileList);
  emit("delete", file);
};

const onChange = ({
  fileList: newFileList,
  event,
}: {
  fileList: any[];
  event: any;
}) => {
  console.log("change 触发", newFileList, event);
  fileList.value = newFileList;
};

const onStart = () => {
  console.log("start 触发");
};

const onProgress = ({ percentage }: { percentage: number }) => {
  console.log("progress 触发", percentage);
};

const onSuccess = ({
  responseText,
  options,
}: {
  responseText: any;
  options: any;
}) => {
  console.log("success 触发", responseText, options);

  try {
    // 尝试解析响应数据
    let result: any;
    if (typeof responseText === "string") {
      result = JSON.parse(responseText);
    } else if (responseText.data) {
      result = JSON.parse(responseText.data);
    } else {
      result = responseText;
    }

    console.log("上传成功，解析结果:", result);

    // 如果有返回的图片URL，更新到fileList中
    // if (result.success && result.data?.url) {
    //   const fileIndex = fileList.value.findIndex(
    //     (file) => file.uid === options.uid
    //   );
    //   if (fileIndex !== -1) {
    //     fileList.value[fileIndex].url = result.data.url;
    //     fileList.value[fileIndex].status = "success";
    //   }
    // }

    emit("upload-success", result);
  } catch (error) {
    console.error("解析上传响应失败:", error);
    emit("upload-error", error);
  }

  Taro.showToast({
    title: "上传成功",
    icon: "none",
  });
};

const onFailure = ({
  responseText,
  options,
}: {
  responseText: any;
  options: any;
}) => {
  console.log("failure 触发", responseText, options);

  let errorMessage = "上传失败";
  try {
    const error = JSON.parse(responseText);
    errorMessage = error.message || errorMessage;
  } catch (e) {
    // 解析失败时使用默认错误信息
  }

  emit("upload-error", { responseText, options, message: errorMessage });

  Taro.showToast({
    title: errorMessage,
    icon: "none",
  });
};

// 图片预览方法
const uploadPreview = (file: any) => {
  console.log("预览图片", file);

  // 构建预览图片数组
  const previewUrls = fileList.value
    .map((item) => item.url || item.path)
    .filter(Boolean);

  if (previewUrls.length > 0) {
    // 使用 Taro.previewImage 进行图片预览
    Taro.previewImage({
      current: file.url || file.path, // 当前显示图片的链接
      urls: previewUrls, // 需要预览的图片链接列表
      success: () => {
        console.log("预览成功");
      },
      fail: (error) => {
        console.error("预览失败", error);
        Taro.showToast({
          title: "预览失败",
          icon: "none",
        });
      },
    });
  } else {
    Taro.showToast({
      title: "暂无可预览的图片",
      icon: "none",
    });
  }
};

// 初始化上传配置
const initUploadConfig = () => {
  const token = Taro.getStorageSync("token") || "";
  const baseUrl = process.env.TARO_APP_PROXY || "";

  uploadUrl.value = baseUrl + "/acc/file/upload";
  uploadHeaders.value = {
    "X-RD-Request-APIToken": token,
  };
};

onMounted(() => {
  initUploadConfig();
});

// 暴露方法给父组件
defineExpose({
  initUploadConfig,
  fileList,
  uploadPreview,
});
</script>

<style lang="less">
.image-uploader {
  .upload-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 128rpx;
    height: 128rpx;
    border: 2rpx dashed #ddd;
    border-radius: 8rpx;
    background-color: #fafafa;

    &:active {
      background-color: #f0f0f0;
    }
  }
}
.nut-uploader__preview-img .close {
  top: -5rpx !important;
  right: 18rpx !important;
}
</style>
